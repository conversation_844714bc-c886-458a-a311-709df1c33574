#include <gtest/gtest.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/macro.h>
void printf_record(precord_t *record) {
  for (player_t *layer = precord_layer_get_first(record); layer != NULL; layer = precord_layer_get_next(record, layer)) {
    const char *layer_name = precord_layer_get_layer_name(layer);
    precord_layer_move_cursor(record, layer_name);
    printf("record layer---------%s----------------------------\n", layer_name);
    for (pfield_t *field = precord_field_get_first(record); field != NULL; field = precord_field_get_next(record, field)) {
      pfield_desc_t *fdesc = precord_field_get_fdesc(field);
      ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);
      printf("record field---------:%s\n", pfdesc_get_name(fdesc));

      if (fvalue == NULL) {
        printf("       value---------:NULL\n");

        continue;
      }
      printf("       value---------:%s\n", ya_fvalue_to_string_repr(fvalue, BASE_NONE));
    }
  }
}
class H323HandoffTest : public ::testing::Test {
protected:
    void SetUp() override {
        engine = nxt_engine_create(NULL);
        ASSERT_NE(engine, nullptr);
    }

    void TearDown() override {
        if (engine) {
        nxt_engine_destroy(engine);
        }
    }

    nxt_engine_t *engine = nullptr;
};

// Test TPKT -> Q931 handoff
TEST_F(H323HandoffTest, TpktToQ931Handoff) {
    // Create a simple TPKT packet with Q.931 payload
    // TPKT header: version(1) + reserved(1) + length(2) = 4 bytes
    // Q.931 payload starts with protocol discriminator 0x08
    uint8_t tpkt_packet[] = {
        0x03, 0x00,        // TPKT version 3, reserved 0
        0x00, 0x10,        // Length: 16 bytes total
        0x08,              // Q.931 protocol discriminator
        0x01,              // Call reference length
        0x00,              // Call reference value
        0x05,              // Message type: SETUP
        0x04, 0x03, 0x80, 0x90, 0xa2,  // Bearer capability IE
        0x18, 0x03, 0xa9, 0x83, 0x81   // Channel identification IE
    };
    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), tpkt_packet,  sizeof(tpkt_packet));


    ASSERT_NE(mbuf, nullptr);

    // Process the packet through the engine
    int result =  nxt_engine_run(engine, mbuf);

    EXPECT_GE(result, 0);

    // Get the protocol record
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    ASSERT_NE(precord, nullptr);

    // Check if TPKT layer was detected
    printf_record(precord);
    nxt_mbuf_free(mbuf);
}

// Test Q931 -> H225 handoff
TEST_F(H323HandoffTest, Q931ToH225Handoff) {
    // Create a Q.931 packet with User-User IE containing H.225 data
    uint8_t q931_packet[] = {
        0x08,              // Q.931 protocol discriminator
        0x01,              // Call reference length
        0x00,              // Call reference value
        0x05,              // Message type: SETUP
        0x7E, 0x10,        // User-User IE (0x7E), length 16
        0x05,              // Protocol discriminator for H.225
        // H.225 ASN.1 data (simplified)
        0x30, 0x0C, 0x02, 0x01, 0x00, 0x02, 0x01, 0x01,
        0x30, 0x04, 0x02, 0x02, 0x00, 0x01
    };

    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), q931_packet,  sizeof(q931_packet));

    ASSERT_NE(mbuf, nullptr);

    // Process the packet through the engine
    int result =  nxt_engine_run(engine, mbuf);
    EXPECT_GE(result, 0);

    // Get the protocol record
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    ASSERT_NE(precord, nullptr);
    printf_record(precord);


    nxt_mbuf_free(mbuf);
}

// Test complete TPKT -> Q931 -> H225 chain
TEST_F(H323HandoffTest, CompleteH323Chain) {
    // Create a complete TPKT packet containing Q.931 with H.225 data
    uint8_t complete_packet[] = {
        // TPKT header
        0x03, 0x00,        // TPKT version 3, reserved 0
        0x00, 0x20,        // Length: 32 bytes total
        
        // Q.931 header
        0x08,              // Q.931 protocol discriminator
        0x01,              // Call reference length
        0x00,              // Call reference value
        0x05,              // Message type: SETUP
        
        // Q.931 IEs
        0x04, 0x03, 0x80, 0x90, 0xa2,  // Bearer capability IE
        0x18, 0x03, 0xa9, 0x83, 0x81,  // Channel identification IE
        
        // User-User IE with H.225 data
        0x7E, 0x10,        // User-User IE (0x7E), length 16
        0x05,              // Protocol discriminator for H.225
        // H.225 ASN.1 data (simplified)
        0x30, 0x0C, 0x02, 0x01, 0x00, 0x02, 0x01, 0x01,
        0x30, 0x04, 0x02, 0x02, 0x00, 0x01
    };

    nxt_mbuf_t *mbuf = nxt_mbuf_new_from_data(complete_packet, sizeof(complete_packet));
    ASSERT_NE(mbuf, nullptr);

    // Process the packet through the engine
    int result = nxt_engine_dissect_packet(engine, mbuf);
    EXPECT_GE(result, 0);

    // Get the protocol record
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    ASSERT_NE(precord, nullptr);

    // Check if all three layers were detected
    bool has_tpkt = precord_layer_has_layer(precord, "tpkt");
    bool has_q931 = precord_layer_has_layer(precord, "q931");
    bool has_h225 = precord_layer_has_layer(precord, "h225");

    EXPECT_TRUE(has_tpkt);
    EXPECT_TRUE(has_q931);
    EXPECT_TRUE(has_h225);

    nxt_mbuf_free(mbuf);
}
